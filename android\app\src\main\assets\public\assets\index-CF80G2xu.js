const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["js/web-DW8QJZyr.js","js/vendor-C67cHu0f.js","js/utils-CgIdLkdF.js","js/icons-Bhz3yUky.js","js/Dashboard-DN1UwnOq.js","js/charts-B70j_Dbf.js","js/SwipeableLayout-D_gm2iTZ.js","js/Purchases-COGkPscP.js","js/Usage-BlNK_qXz.js","js/ScrollHint-JkzulfZT.js","js/History-LbXCAQK-.js","js/Settings-dA8Y6xUM.js","js/web-BDcoYDXJ.js","js/web-CeFd6JiW.js"])))=>i.map(i=>d[i]);
import{r as h,a as Se,u as W,N as z,b as he,c as ke,i as Ee,d as Pe,e as I,B as Te}from"../js/vendor-C67cHu0f.js";import{f as Z}from"../js/utils-CgIdLkdF.js";import{H as Ce,a as $e,b as pe,c as Le,d as De,e as be,f as V,g as X,h as Q,i as Ae,j as Ue,k as Re,l as Ie,m as Oe}from"../js/icons-Bhz3yUky.js";(function(){const e=document.createElement("link").relList;if(e&&e.supports&&e.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))a(s);new MutationObserver(s=>{for(const i of s)if(i.type==="childList")for(const l of i.addedNodes)l.tagName==="LINK"&&l.rel==="modulepreload"&&a(l)}).observe(document,{childList:!0,subtree:!0});function n(s){const i={};return s.integrity&&(i.integrity=s.integrity),s.referrerPolicy&&(i.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?i.credentials="include":s.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function a(s){if(s.ep)return;s.ep=!0;const i=n(s);fetch(s.href,i)}})();var xe={exports:{}},G={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var _e=h,He=Symbol.for("react.element"),Fe=Symbol.for("react.fragment"),Be=Object.prototype.hasOwnProperty,Me=_e.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,qe={key:!0,ref:!0,__self:!0,__source:!0};function ye(r,e,n){var a,s={},i=null,l=null;n!==void 0&&(i=""+n),e.key!==void 0&&(i=""+e.key),e.ref!==void 0&&(l=e.ref);for(a in e)Be.call(e,a)&&!qe.hasOwnProperty(a)&&(s[a]=e[a]);if(r&&r.defaultProps)for(a in e=r.defaultProps,e)s[a]===void 0&&(s[a]=e[a]);return{$$typeof:He,type:r,key:i,ref:l,props:s,_owner:Me.current}}G.Fragment=Fe;G.jsx=ye;G.jsxs=ye;xe.exports=G;var t=xe.exports,we,ee=Se;we=ee.createRoot,ee.hydrateRoot;const ze="modulepreload",We=function(r){return"/"+r},te={},U=function(e,n,a){let s=Promise.resolve();if(n&&n.length>0){document.getElementsByTagName("link");const l=document.querySelector("meta[property=csp-nonce]"),u=l?.nonce||l?.getAttribute("nonce");s=Promise.allSettled(n.map(o=>{if(o=We(o),o in te)return;te[o]=!0;const f=o.endsWith(".css"),p=f?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${o}"]${p}`))return;const c=document.createElement("link");if(c.rel=f?"stylesheet":ze,f||(c.as="script"),c.crossOrigin="",c.href=o,u&&c.setAttribute("nonce",u),document.head.appendChild(c),f)return new Promise((g,b)=>{c.addEventListener("load",g),c.addEventListener("error",()=>b(new Error(`Unable to preload CSS for ${o}`)))})}))}function i(l){const u=new Event("vite:preloadError",{cancelable:!0});if(u.payload=l,window.dispatchEvent(u),!u.defaultPrevented)throw l}return s.then(l=>{for(const u of l||[])u.status==="rejected"&&i(u.reason);return e().catch(i)})};/*! Capacitor: https://capacitorjs.com/ - MIT License */var H;(function(r){r.Unimplemented="UNIMPLEMENTED",r.Unavailable="UNAVAILABLE"})(H||(H={}));class K extends Error{constructor(e,n,a){super(e),this.message=e,this.code=n,this.data=a}}const Ge=r=>{var e,n;return r?.androidBridge?"android":!((n=(e=r?.webkit)===null||e===void 0?void 0:e.messageHandlers)===null||n===void 0)&&n.bridge?"ios":"web"},Ke=r=>{const e=r.CapacitorCustomPlatform||null,n=r.Capacitor||{},a=n.Plugins=n.Plugins||{},s=()=>e!==null?e.name:Ge(r),i=()=>s()!=="web",l=c=>{const g=f.get(c);return!!(g?.platforms.has(s())||u(c))},u=c=>{var g;return(g=n.PluginHeaders)===null||g===void 0?void 0:g.find(b=>b.name===c)},o=c=>r.console.error(c),f=new Map,p=(c,g={})=>{const b=f.get(c);if(b)return console.warn(`Capacitor plugin "${c}" already registered. Cannot register plugins twice.`),b.proxy;const w=s(),y=u(c);let x;const $=async()=>(!x&&w in g?x=typeof g[w]=="function"?x=await g[w]():x=g[w]:e!==null&&!x&&"web"in g&&(x=typeof g.web=="function"?x=await g.web():x=g.web),x),T=(d,m)=>{var N,k;if(y){const C=y?.methods.find(v=>m===v.name);if(C)return C.rtype==="promise"?v=>n.nativePromise(c,m.toString(),v):(v,M)=>n.nativeCallback(c,m.toString(),v,M);if(d)return(N=d[m])===null||N===void 0?void 0:N.bind(d)}else{if(d)return(k=d[m])===null||k===void 0?void 0:k.bind(d);throw new K(`"${c}" plugin is not implemented on ${w}`,H.Unimplemented)}},j=d=>{let m;const N=(...k)=>{const C=$().then(v=>{const M=T(v,d);if(M){const q=M(...k);return m=q?.remove,q}else throw new K(`"${c}.${d}()" is not implemented on ${w}`,H.Unimplemented)});return d==="addListener"&&(C.remove=async()=>m()),C};return N.toString=()=>`${d.toString()}() { [capacitor code] }`,Object.defineProperty(N,"name",{value:d,writable:!1,configurable:!1}),N},D=j("addListener"),A=j("removeListener"),R=(d,m)=>{const N=D({eventName:d},m),k=async()=>{const v=await N;A({eventName:d,callbackId:v},m)},C=new Promise(v=>N.then(()=>v({remove:k})));return C.remove=async()=>{console.warn("Using addListener() without 'await' is deprecated."),await k()},C},S=new Proxy({},{get(d,m){switch(m){case"$$typeof":return;case"toJSON":return()=>({});case"addListener":return y?R:D;case"removeListener":return A;default:return j(m)}}});return a[c]=S,f.set(c,{name:c,proxy:S,platforms:new Set([...Object.keys(g),...y?[w]:[]])}),S};return n.convertFileSrc||(n.convertFileSrc=c=>c),n.getPlatform=s,n.handleError=o,n.isNativePlatform=i,n.isPluginAvailable=l,n.registerPlugin=p,n.Exception=K,n.DEBUG=!!n.DEBUG,n.isLoggingEnabled=!!n.isLoggingEnabled,n},Ve=r=>r.Capacitor=Ke(r),P=Ve(typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{}),F=P.registerPlugin;class ve{constructor(){this.listeners={},this.retainedEventArguments={},this.windowListeners={}}addListener(e,n){let a=!1;this.listeners[e]||(this.listeners[e]=[],a=!0),this.listeners[e].push(n);const i=this.windowListeners[e];i&&!i.registered&&this.addWindowListener(i),a&&this.sendRetainedArgumentsForEvent(e);const l=async()=>this.removeListener(e,n);return Promise.resolve({remove:l})}async removeAllListeners(){this.listeners={};for(const e in this.windowListeners)this.removeWindowListener(this.windowListeners[e]);this.windowListeners={}}notifyListeners(e,n,a){const s=this.listeners[e];if(!s){if(a){let i=this.retainedEventArguments[e];i||(i=[]),i.push(n),this.retainedEventArguments[e]=i}return}s.forEach(i=>i(n))}hasListeners(e){return!!this.listeners[e].length}registerWindowListener(e,n){this.windowListeners[n]={registered:!1,windowEventName:e,pluginEventName:n,handler:a=>{this.notifyListeners(n,a)}}}unimplemented(e="not implemented"){return new P.Exception(e,H.Unimplemented)}unavailable(e="not available"){return new P.Exception(e,H.Unavailable)}async removeListener(e,n){const a=this.listeners[e];if(!a)return;const s=a.indexOf(n);this.listeners[e].splice(s,1),this.listeners[e].length||this.removeWindowListener(this.windowListeners[e])}addWindowListener(e){window.addEventListener(e.windowEventName,e.handler),e.registered=!0}removeWindowListener(e){e&&(window.removeEventListener(e.windowEventName,e.handler),e.registered=!1)}sendRetainedArgumentsForEvent(e){const n=this.retainedEventArguments[e];n&&(delete this.retainedEventArguments[e],n.forEach(a=>{this.notifyListeners(e,a)}))}}const re=r=>encodeURIComponent(r).replace(/%(2[346B]|5E|60|7C)/g,decodeURIComponent).replace(/[()]/g,escape),ne=r=>r.replace(/(%[\dA-F]{2})+/gi,decodeURIComponent);class Ye extends ve{async getCookies(){const e=document.cookie,n={};return e.split(";").forEach(a=>{if(a.length<=0)return;let[s,i]=a.replace(/=/,"CAP_COOKIE").split("CAP_COOKIE");s=ne(s).trim(),i=ne(i).trim(),n[s]=i}),n}async setCookie(e){try{const n=re(e.key),a=re(e.value),s=`; expires=${(e.expires||"").replace("expires=","")}`,i=(e.path||"/").replace("path=",""),l=e.url!=null&&e.url.length>0?`domain=${e.url}`:"";document.cookie=`${n}=${a||""}${s}; path=${i}; ${l};`}catch(n){return Promise.reject(n)}}async deleteCookie(e){try{document.cookie=`${e.key}=; Max-Age=0`}catch(n){return Promise.reject(n)}}async clearCookies(){try{const e=document.cookie.split(";")||[];for(const n of e)document.cookie=n.replace(/^ +/,"").replace(/=.*/,`=;expires=${new Date().toUTCString()};path=/`)}catch(e){return Promise.reject(e)}}async clearAllCookies(){try{await this.clearCookies()}catch(e){return Promise.reject(e)}}}F("CapacitorCookies",{web:()=>new Ye});const Je=async r=>new Promise((e,n)=>{const a=new FileReader;a.onload=()=>{const s=a.result;e(s.indexOf(",")>=0?s.split(",")[1]:s)},a.onerror=s=>n(s),a.readAsDataURL(r)}),Ze=(r={})=>{const e=Object.keys(r);return Object.keys(r).map(s=>s.toLocaleLowerCase()).reduce((s,i,l)=>(s[i]=r[e[l]],s),{})},Xe=(r,e=!0)=>r?Object.entries(r).reduce((a,s)=>{const[i,l]=s;let u,o;return Array.isArray(l)?(o="",l.forEach(f=>{u=e?encodeURIComponent(f):f,o+=`${i}=${u}&`}),o.slice(0,-1)):(u=e?encodeURIComponent(l):l,o=`${i}=${u}`),`${a}&${o}`},"").substr(1):null,Qe=(r,e={})=>{const n=Object.assign({method:r.method||"GET",headers:r.headers},e),s=Ze(r.headers)["content-type"]||"";if(typeof r.data=="string")n.body=r.data;else if(s.includes("application/x-www-form-urlencoded")){const i=new URLSearchParams;for(const[l,u]of Object.entries(r.data||{}))i.set(l,u);n.body=i.toString()}else if(s.includes("multipart/form-data")||r.data instanceof FormData){const i=new FormData;if(r.data instanceof FormData)r.data.forEach((u,o)=>{i.append(o,u)});else for(const u of Object.keys(r.data))i.append(u,r.data[u]);n.body=i;const l=new Headers(n.headers);l.delete("content-type"),n.headers=l}else(s.includes("application/json")||typeof r.data=="object")&&(n.body=JSON.stringify(r.data));return n};class et extends ve{async request(e){const n=Qe(e,e.webFetchExtra),a=Xe(e.params,e.shouldEncodeUrlParams),s=a?`${e.url}?${a}`:e.url,i=await fetch(s,n),l=i.headers.get("content-type")||"";let{responseType:u="text"}=i.ok?e:{};l.includes("application/json")&&(u="json");let o,f;switch(u){case"arraybuffer":case"blob":f=await i.blob(),o=await Je(f);break;case"json":o=await i.json();break;case"document":case"text":default:o=await i.text()}const p={};return i.headers.forEach((c,g)=>{p[g]=c}),{data:o,headers:p,status:i.status,url:i.url}}async get(e){return this.request(Object.assign(Object.assign({},e),{method:"GET"}))}async post(e){return this.request(Object.assign(Object.assign({},e),{method:"POST"}))}async put(e){return this.request(Object.assign(Object.assign({},e),{method:"PUT"}))}async patch(e){return this.request(Object.assign(Object.assign({},e),{method:"PATCH"}))}async delete(e){return this.request(Object.assign(Object.assign({},e),{method:"DELETE"}))}}F("CapacitorHttp",{web:()=>new et});var oe;(function(r){r[r.Sunday=1]="Sunday",r[r.Monday=2]="Monday",r[r.Tuesday=3]="Tuesday",r[r.Wednesday=4]="Wednesday",r[r.Thursday=5]="Thursday",r[r.Friday=6]="Friday",r[r.Saturday=7]="Saturday"})(oe||(oe={}));const E=F("LocalNotifications",{web:()=>U(()=>import("../js/web-DW8QJZyr.js"),__vite__mapDeps([0,1,2,3])).then(r=>new r.LocalNotificationsWeb)}),tt=(r,e,n,a)=>{const s=h.useRef(null),i=h.useRef(!1);return console.log("🔔 useNotifications hook called with:",{enabled:r,time:e,lastDate:n,platform:typeof window<"u"?"web":"unknown"}),h.useEffect(()=>{if(console.log("🔔 useNotifications useEffect triggered with:",{enabled:r,time:e,lastDate:n}),s.current&&(console.log("🔔 Clearing existing interval:",s.current),clearInterval(s.current),s.current=null),!r){console.log("🔔 Notifications disabled, cleaning up and exiting");return}const o=async()=>{try{console.log("Notifications: Setting up for platform:",P.getPlatform()),P.isNativePlatform()?(console.log("Notifications: Native platform detected"),await f()):(console.log("Notifications: Web platform detected, using fallback"),c())}catch(b){console.error("Notifications: Setup error:",b),c()}},f=async()=>{try{console.log("Notifications: Requesting permissions...");const b=await E.requestPermissions();if(console.log("Notifications: Permission result:",b),b.display!=="granted"){console.log("Notifications: Permission denied");return}console.log("Notifications: Permission granted, setting up daily reminder"),await p(),i.current||(await g(),i.current=!0)}catch(b){throw console.error("Notifications: Native setup error:",b),b}},p=async()=>{try{console.log("Notifications: Scheduling daily notification...");try{await E.cancel({notifications:[{id:1}]}),console.log("Notifications: Cancelled existing notifications")}catch{console.log("Notifications: No existing notifications to cancel")}const[b,w]=e.split(":").map(Number);console.log("Notifications: Parsed time:",b,":",w);const y=new Date,x=new Date;x.setHours(b,w,0,0),x<=y&&(x.setDate(x.getDate()+1),console.log("Notifications: Time has passed today, scheduling for tomorrow")),console.log("Notifications: Scheduling for:",x.toLocaleString());const $={notifications:[{title:"⚡ Prepaid Meter Reminder",body:"Don't forget to record your electricity usage today!",id:1,schedule:{at:x,repeats:!0,every:"day"},sound:"default",attachments:null,actionTypeId:"",extra:{type:"daily_reminder"}}]};await E.schedule($),console.log("Notifications: Daily reminder scheduled successfully for",x.toLocaleString());const T=await E.getPending();console.log("Notifications: Pending notifications:",T.notifications.length)}catch(b){throw console.error("Notifications: Scheduling error:",b),b}},c=()=>{console.log("Notifications: Setting up web notifications"),(async()=>{if("Notification"in window){if(console.log("🔔 Current permission status:",Notification.permission),Notification.permission==="default"){console.log("🔔 Requesting notification permission...");try{const x=await Notification.requestPermission();return console.log("🔔 Permission request result:",x),x==="granted"}catch(x){return console.error("🔔 Permission request failed:",x),!1}}const y=Notification.permission==="granted";return console.log("🔔 Permission already set, granted:",y),y}return console.log("🔔 Notifications not supported in this browser"),!1})().then(y=>{console.log("🔔 Final permission status:",y?"GRANTED":"DENIED"),y||console.warn("🔔 ⚠️ Notifications will not work - permission denied or not supported")});const w=()=>{const y=new Date;let x,$;if(typeof e=="string")[x,$]=e.split(":").map(Number);else if(e&&typeof e=="object")x=e.hours,$=e.minutes;else{console.error("🔔 Invalid notificationTime format:",e);return}const T=new Date;T.setHours(x,$,0,0);const j=new Date(T);j.setMinutes(j.getMinutes()-2);const D=new Date(T);D.setMinutes(D.getMinutes()+3);const A=y.toDateString(),R=n?new Date(n).toDateString():null;console.log("🔔 DEBUG RAW VALUES:",{lastNotificationDate:n,lastNotificationDateType:typeof n,lastNotificationDateStr:R,today:A,comparison:R!==A});const S=y>=j&&y<=D,d=R!==A;if(console.log("🔔 WEB NOTIFICATION CHECK:",{currentTime:y.toLocaleTimeString(),targetTime:T.toLocaleTimeString(),windowStart:j.toLocaleTimeString(),windowEnd:D.toLocaleTimeString(),today:A,lastNotificationDate:R,inWindow:S,notSentToday:d,permission:typeof Notification<"u"?Notification.permission:"not-supported"}),console.log("🔔 CONDITION CHECK:",{inWindow:S,notSentToday:d,bothTrue:S&&d,willTrigger:S&&d}),S&&d)if(console.log("Notifications: Conditions met, attempting to send notification"),"Notification"in window&&Notification.permission==="granted"){console.log("Notifications: Sending web notification");const m=new Notification("⚡ Prepaid Meter Reminder",{body:"Don't forget to record your electricity usage today!",icon:"/favicon.ico",badge:"/favicon.ico",tag:"daily-reminder",requireInteraction:!1,silent:!1});setTimeout(()=>{m.close()},1e4),a({type:"UPDATE_SETTINGS",payload:{lastNotificationDate:y.toISOString()}}),console.log("Notifications: Web notification sent successfully")}else console.log("Notifications: Web notification permission not granted")};console.log("🔔 Starting web notification checker..."),w(),console.log("🔔 Creating interval..."),s.current=setInterval(()=>{console.log("🔔 *** INTERVAL TICK *** Running scheduled notification check at:",new Date().toLocaleTimeString()),w()},3e4),console.log("🔔 Web notification checker started with 30-second interval, intervalId:",s.current),setTimeout(()=>{console.log("🔔 5-second test: Interval should be running...")},5e3)},g=async()=>{if(P.isNativePlatform())try{console.log("Notifications: Setting up listeners..."),await E.removeAllListeners(),await E.addListener("localNotificationActionPerformed",b=>{console.log("Notification action performed:",b),a({type:"UPDATE_SETTINGS",payload:{lastNotificationDate:new Date().toISOString()}})}),await E.addListener("localNotificationReceived",b=>{console.log("Notification received in foreground:",b),a({type:"UPDATE_SETTINGS",payload:{lastNotificationDate:new Date().toISOString()}})}),console.log("Notifications: Listeners setup complete")}catch(b){console.error("Notifications: Listener setup error:",b)}};return o(),()=>{console.log("Notifications: Cleaning up..."),s.current&&(clearInterval(s.current),s.current=null),P.isNativePlatform()&&i.current&&(E.removeAllListeners(),i.current=!1)}},[r,e,n,a]),{testNotification:async()=>{try{if(console.log("🔔 TEST NOTIFICATION FUNCTION CALLED"),alert("Test notification function called! Check console for details."),!P.isNativePlatform()){if(console.log("🌐 Testing web notification on browser"),!("Notification"in window))return console.error("❌ Web notifications not supported in this browser"),alert("Web notifications not supported in this browser"),!1;if(console.log("🔔 Current notification permission:",Notification.permission),Notification.permission==="default"){console.log("🔔 Requesting notification permission...");const p=await Notification.requestPermission();console.log("🔔 Permission request result:",p)}if(Notification.permission==="granted"){console.log("✅ Permission granted, creating notification...");try{const p=new Notification("⚡ Test Notification",{body:"This is a test notification from your Prepaid Meter app!",icon:"/favicon.ico",tag:"test-notification",requireInteraction:!1});return console.log("✅ Notification created successfully"),setTimeout(()=>{p.close(),console.log("🔔 Notification auto-closed")},5e3),!0}catch(p){return console.error("❌ Error creating notification:",p),alert("Error creating notification: "+p.message),!1}}else return console.log("❌ Notification permission denied or blocked"),alert("Notification permission denied. Please enable notifications in your browser settings."),!1}console.log("Notifications: Testing native notification");const o=await E.requestPermissions();if(console.log("Notifications: Native permission result:",o),o.display!=="granted")return console.log("Test notification: Native permission denied"),!1;try{await E.cancel({notifications:[{id:999}]})}catch{}const f={notifications:[{title:"⚡ Test Notification",body:"This is a test notification from your Prepaid Meter app!",id:999,schedule:{at:new Date(Date.now()+2e3)},sound:"default",attachments:null,actionTypeId:"",extra:{type:"test_notification"}}]};return await E.schedule(f),console.log("Notifications: Test notification scheduled for 2 seconds from now"),!0}catch(o){return console.error("Test notification error:",o),!1}},forceNotification:async()=>{try{if(console.log("🔔 FORCE NOTIFICATION CALLED - ignoring time window"),alert("🔔 Force notification function started! Check console for details."),!("Notification"in window))return console.error("❌ Web notifications not supported"),alert("❌ Web notifications not supported in this browser"),!1;if(console.log("🔔 Current permission status:",Notification.permission),Notification.permission!=="granted"){console.log("🔔 Requesting permission..."),alert("🔔 Requesting notification permission...");const f=await Notification.requestPermission();if(console.log("🔔 Permission result:",f),f!=="granted")return console.log("❌ Permission denied"),alert("❌ Permission denied: "+f),!1}console.log("✅ Permission granted, creating forced notification..."),alert("✅ Permission OK, creating notification now...");const o=new Notification("⚡ FORCED Test Notification",{body:"This notification was forced regardless of time window! If you see this, notifications are working!",icon:"/favicon.ico",tag:"force-test-"+Date.now(),requireInteraction:!1,silent:!1});return console.log("✅ Notification object created:",o),o.onshow=()=>{console.log("✅ Notification shown successfully"),alert("✅ Notification shown! Check your notification area.")},o.onerror=f=>{console.error("❌ Notification error:",f),alert("❌ Notification error: "+f)},o.onclick=()=>{console.log("🔔 Notification clicked"),o.close()},setTimeout(()=>{o.close(),console.log("🔔 Notification auto-closed after 8 seconds")},8e3),a({type:"UPDATE_SETTINGS",payload:{lastNotificationDate:new Date().toISOString()}}),console.log("✅ Forced notification process completed"),!0}catch(o){return console.error("❌ Force notification error:",o),alert("❌ Force notification error: "+o.message),!1}}}};function rt(r,e=null){try{const n=localStorage.getItem(r);return n?JSON.parse(n):e}catch(n){return console.error(`Error reading from localStorage key "${r}":`,n),e}}function nt(r,e){try{return localStorage.setItem(r,JSON.stringify(e)),!0}catch(n){return console.error(`Error writing to localStorage key "${r}":`,n),!1}}function se(r){try{return localStorage.removeItem(r),!0}catch(e){return console.error(`Error removing localStorage key "${r}":`,e),!1}}const Ne=h.createContext(),Y={version:"1.1.0",currentUnits:0,previousUnits:0,unitCost:0,thresholdLimit:0,currency:"ZAR",currencySymbol:"R",customCurrencyName:"",customCurrencySymbol:"",unitName:"kWh",customUnitName:"",purchases:[],usageHistory:[],isInitialized:!1,lastResetDate:null,lastMonthlyReset:null,notificationsEnabled:!1,notificationTime:"18:00",lastNotificationDate:null};function ot(r,e){switch(e.type){case"INITIALIZE_APP":return{...r,currentUnits:e.payload.initialUnits,previousUnits:e.payload.initialUnits,unitCost:e.payload.unitCost,isInitialized:!0,lastResetDate:new Date().toISOString()};case"ADD_PURCHASE":{const n={id:Date.now(),date:new Date().toISOString(),currency:e.payload.currency,units:e.payload.units,unitCost:r.unitCost,timestamp:Z(new Date,"yyyy-MM-dd HH:mm:ss")};return{...r,purchases:[n,...r.purchases],currentUnits:r.currentUnits+e.payload.units}}case"UPDATE_USAGE":{const n={id:Date.now(),date:new Date().toISOString(),previousUnits:r.currentUnits,currentUnits:e.payload.currentUnits,usage:r.currentUnits-e.payload.currentUnits,timestamp:Z(new Date,"yyyy-MM-dd HH:mm:ss")};return{...r,previousUnits:r.currentUnits,currentUnits:e.payload.currentUnits,usageHistory:[n,...r.usageHistory]}}case"UPDATE_SETTINGS":return{...r,...e.payload};case"FACTORY_RESET":return{...Y};case"DASHBOARD_RESET":return{...r,currentUnits:0,previousUnits:0,lastResetDate:new Date().toISOString()};case"MONTHLY_RESET":return{...r,usageHistory:[],lastMonthlyReset:new Date().toISOString()};case"LOAD_STATE":return{...r,...e.payload};default:return console.warn(`Unknown action type: ${e.type}`),r}}function st({children:r}){const[e,n]=h.useReducer(ot,Y);h.useEffect(()=>{const d="prepaid-meter-app-v1.1",m=rt(d);if(se("prepaid-meter-app"),m){if(!m.version||m.version!==Y.version){console.log("Version mismatch detected, clearing old data"),se(d);return}n({type:"LOAD_STATE",payload:m})}},[]),h.useEffect(()=>{nt("prepaid-meter-app-v1.1",e)||console.warn("Failed to save app state - data may not persist")},[e]),h.useEffect(()=>{const d=new Date,m=d.getMonth(),N=d.getFullYear();if(e.lastMonthlyReset){const k=new Date(e.lastMonthlyReset),C=k.getMonth(),v=k.getFullYear();(N>v||N===v&&m>C)&&n({type:"MONTHLY_RESET"})}else e.isInitialized&&n({type:"UPDATE_SETTINGS",payload:{lastMonthlyReset:d.toISOString()}})},[e.lastMonthlyReset,e.isInitialized]);const{testNotification:a,forceNotification:s}=tt(e.notificationsEnabled&&e.isInitialized,e.notificationTime,e.lastNotificationDate,n),i=e.usageHistory.length>0?e.previousUnits-e.currentUnits:0,l=e.currentUnits<=e.thresholdLimit&&e.thresholdLimit>0,u=e.purchases.reduce((d,m)=>d+m.currency,0),o=e.usageHistory.reduce((d,m)=>d+m.usage,0),f=new Date,p=new Date(f.getFullYear(),f.getMonth(),f.getDate()-f.getDay()),c=new Date(f.getFullYear(),f.getMonth(),1),g=e.purchases.filter(d=>new Date(d.date)>=p),b=e.purchases.filter(d=>new Date(d.date)>=c),w=e.usageHistory.filter(d=>new Date(d.date)>=p),y=e.usageHistory.filter(d=>new Date(d.date)>=c),x=g.reduce((d,m)=>d+m.currency,0),$=b.reduce((d,m)=>d+m.currency,0),T=w.reduce((d,m)=>d+m.usage,0),j=y.reduce((d,m)=>d+m.usage,0),S={state:e,dispatch:n,initializeApp:(d,m)=>{n({type:"INITIALIZE_APP",payload:{initialUnits:d,unitCost:m}})},addPurchase:(d,m)=>{n({type:"ADD_PURCHASE",payload:{currency:d,units:m}})},updateUsage:d=>{n({type:"UPDATE_USAGE",payload:{currentUnits:d}})},updateSettings:d=>{n({type:"UPDATE_SETTINGS",payload:d})},factoryReset:()=>{n({type:"FACTORY_RESET"})},dashboardReset:()=>{n({type:"DASHBOARD_RESET"})},usageSinceLastRecording:i,isThresholdExceeded:l,totalPurchases:u,totalUnitsUsed:o,getDisplayUnitName:()=>e.unitName==="custom"?e.customUnitName||"Units":e.unitName,getDisplayCurrencySymbol:()=>e.currency==="CUSTOM"?e.customCurrencySymbol||"C":e.currencySymbol||"R",getDisplayCurrencyName:()=>e.currency==="CUSTOM"?e.customCurrencyName||"Custom Currency":[{code:"ZAR",name:"South African Rand"},{code:"USD",name:"US Dollar"},{code:"EUR",name:"Euro"},{code:"GBP",name:"British Pound"},{code:"JPY",name:"Japanese Yen"}].find(m=>m.code===e.currency)?.name||"Unknown Currency",testNotification:a,forceNotification:s,weeklyPurchaseTotal:x,monthlyPurchaseTotal:$,weeklyUsageTotal:T,monthlyUsageTotal:j,weeklyPurchases:g,monthlyPurchases:b,weeklyUsage:w,monthlyUsage:y};return t.jsx(Ne.Provider,{value:S,children:r})}function J(){const r=h.useContext(Ne);if(!r)throw new Error("useApp must be used within an AppProvider");return r}const je=h.createContext(),B={electric:{name:"Electric Blue",primary:"bg-blue-700",secondary:"bg-blue-50",accent:"bg-blue-500",background:"bg-blue-25",text:"text-blue-900",textSecondary:"text-blue-700",border:"border-blue-200",card:"bg-blue-50",gradient:"from-blue-500 to-blue-700",light:"bg-blue-100",lighter:"bg-blue-50",dark:"bg-blue-700",darker:"bg-blue-800"},dark:{name:"Dark Mode",primary:"bg-gray-700",secondary:"bg-gray-700",accent:"bg-gray-500",background:"bg-gray-900",text:"text-white",textSecondary:"text-gray-300",border:"border-gray-600",card:"bg-gray-800",gradient:"from-gray-600 to-gray-800",light:"bg-gray-700",lighter:"bg-gray-600",dark:"bg-gray-800",darker:"bg-gray-900"},green:{name:"Eco Green",primary:"bg-green-700",secondary:"bg-green-50",accent:"bg-green-500",background:"bg-green-25",text:"text-green-900",textSecondary:"text-green-700",border:"border-green-200",card:"bg-green-50",gradient:"from-green-500 to-green-700",light:"bg-green-100",lighter:"bg-green-50",dark:"bg-green-700",darker:"bg-green-800"},teal:{name:"Ocean Teal",primary:"bg-teal-700",secondary:"bg-teal-50",accent:"bg-teal-500",background:"bg-teal-25",text:"text-teal-900",textSecondary:"text-teal-700",border:"border-teal-200",card:"bg-teal-50",gradient:"from-teal-500 to-teal-700",light:"bg-teal-100",lighter:"bg-teal-50",dark:"bg-teal-700",darker:"bg-teal-800"},pink:{name:"Rose Pink",primary:"bg-pink-700",secondary:"bg-pink-50",accent:"bg-pink-500",background:"bg-pink-25",text:"text-pink-900",textSecondary:"text-pink-700",border:"border-pink-200",card:"bg-pink-50",gradient:"from-pink-500 to-pink-700",light:"bg-pink-100",lighter:"bg-pink-50",dark:"bg-pink-700",darker:"bg-pink-800"}};function it({children:r}){const[e,n]=h.useState("electric");h.useEffect(()=>{const s=localStorage.getItem("prepaid-meter-theme");s&&B[s]&&n(s)},[]),h.useEffect(()=>{localStorage.setItem("prepaid-meter-theme",e)},[e]);const a={currentTheme:e,setCurrentTheme:n,theme:B[e],themes:B};return t.jsx(je.Provider,{value:a,children:t.jsx("div",{className:`${B[e].background} ${B[e].text} text-base min-h-screen transition-all duration-300`,style:{fontFamily:"Inter, system-ui, -apple-system, sans-serif",fontSize:"16px"},children:r})})}function L(){const r=h.useContext(je);if(!r)throw new Error("useTheme must be used within a ThemeProvider");return r}function at({size:r="md",animated:e=!0,showText:n=!0}){const{theme:a}=L(),[s,i]=h.useState(!1),l={sm:{logo:"h-12 w-12",text:"text-sm",icon:"h-6 w-6"},md:{logo:"h-16 w-16",text:"text-lg",icon:"h-8 w-8"},lg:{logo:"h-20 w-20",text:"text-xl",icon:"h-10 w-10"},xl:{logo:"h-24 w-24",text:"text-2xl",icon:"h-12 w-12"}},u=l[r]||l.md;return t.jsxs("div",{className:"flex items-center gap-3",children:[t.jsx("div",{className:`${u.logo} flex items-center justify-center`,children:s?t.jsx("div",{className:`${u.logo} rounded-2xl bg-gradient-to-br ${a.gradient} shadow-lg flex items-center justify-center ${e?"animate-pulse":""}`,children:t.jsx(Ce,{className:`${u.icon} text-white`})}):t.jsx("img",{src:"/Logo Prepaid User.png",alt:"Prepaid User Electricity Logo",className:`${u.logo} object-contain`,onError:()=>i(!0),onLoad:()=>i(!1)})}),n&&t.jsxs("div",{className:"flex flex-col",children:[t.jsx("h1",{className:`${u.text} font-black ${a.text} leading-tight`,children:"Prepaid User"}),t.jsx("p",{className:`text-base font-bold ${a.textSecondary} tracking-wider leading-tight`,children:"Electricity"})]})]})}function ct({onMenuClick:r}){const{theme:e,currentTheme:n}=L(),{state:a}=J(),s=()=>n==="dark"?"bg-gray-900/95 backdrop-blur-xl border-gray-700/50":{electric:"bg-blue-550/95 backdrop-blur-xl border-white/30",green:"bg-green-550/95 backdrop-blur-xl border-white/30",teal:"bg-teal-550/95 backdrop-blur-xl border-white/30",pink:"bg-pink-550/95 backdrop-blur-xl border-white/30"}[n]||"bg-blue-550/95 backdrop-blur-xl border-white/30",i=()=>n==="dark"?e.text:"text-white drop-shadow-sm";return t.jsxs("header",{className:`${s()} border-b px-4 py-0.5 flex items-center justify-between shadow-xl`,children:[t.jsxs("div",{className:"flex items-center space-x-2",children:[t.jsx("button",{onClick:r,className:"hamburger-menu lg:hidden p-1.5 rounded-lg bg-white/15 backdrop-blur-sm text-white hover:bg-white/25 transition-all duration-300 shadow-lg",children:t.jsx($e,{className:"h-6 w-6"})}),t.jsx("div",{className:"flex items-center",children:t.jsx(at,{size:"sm",animated:!0,showText:!1})})]}),t.jsxs("div",{className:"flex items-center space-x-2 lg:space-x-3 bg-black/30 backdrop-blur-md rounded-lg px-3 lg:px-4 py-1 lg:py-2 border border-white/30 shadow-lg",children:[t.jsxs("div",{className:"text-right",children:[t.jsx("p",{className:`text-sm lg:text-base ${i()}`,children:"Current Units"}),t.jsx("p",{className:`text-base lg:text-lg font-bold ${i()}`,children:a.currentUnits.toFixed(2)})]}),t.jsx("div",{className:"w-2 lg:w-3 h-2 lg:h-3 rounded-full bg-white/80 pulse-glow shadow-sm"})]})]})}const ie=[{name:"Dashboard",href:"/dashboard",icon:pe},{name:"Purchases",href:"/purchases",icon:Le},{name:"Usage",href:"/usage",icon:De},{name:"History",href:"/history",icon:be}],ae=[{name:"General Settings",href:"/settings?section=general",icon:V},{name:"Appearance",href:"/settings?section=appearance",icon:Ae},{name:"Reset Options",href:"/settings?section=reset",icon:Ue}];function lt({isOpen:r,onClose:e}){const{theme:n,currentTheme:a}=L(),s=W(),[i,l]=h.useState(!1),u=()=>a==="dark"?"bg-gray-900/95 backdrop-blur-xl border-gray-700/50":{electric:"bg-blue-550/95 backdrop-blur-xl border-white/30",green:"bg-green-550/95 backdrop-blur-xl border-white/30",teal:"bg-teal-550/95 backdrop-blur-xl border-white/30",pink:"bg-pink-550/95 backdrop-blur-xl border-white/30"}[a]||"bg-blue-550/95 backdrop-blur-xl border-white/30",o=()=>a==="dark"?n.text:"text-white drop-shadow-sm",f=()=>a==="dark"?"bg-white/15 backdrop-blur-sm":"bg-white/25 backdrop-blur-sm",p=()=>a==="dark"?"bg-white/20 backdrop-blur-md":"bg-white/35 backdrop-blur-md";return t.jsxs(t.Fragment,{children:[t.jsx("div",{className:`hidden lg:flex lg:flex-shrink-0 lg:w-64 ${u()} border-r shadow-2xl`,children:t.jsxs("div",{className:"flex flex-col w-full",children:[t.jsx("div",{className:"flex items-center justify-center h-16 px-4 border-b border-white/15",children:t.jsx("h2",{className:`text-xl font-bold ${o()} tracking-wider drop-shadow-sm`,children:"MENU"})}),t.jsxs("nav",{className:"flex-1 px-4 py-6 space-y-2",children:[ie.map(c=>t.jsxs(z,{to:c.href,className:({isActive:g})=>`flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-300 ${g?`${p()} ${o()} shadow-lg border border-white/30`:`${o()} hover:${f()} hover:shadow-md hover:border hover:border-white/20`}`,children:[t.jsx(c.icon,{className:"mr-3 h-5 w-5"}),c.name]},c.name)),t.jsxs("div",{className:"space-y-2",children:[t.jsxs("button",{onClick:()=>l(!i),className:`w-full flex items-center justify-between px-4 py-3 text-sm font-medium rounded-xl transition-all duration-300 ${s.pathname.includes("/settings")?`${p()} ${o()} shadow-lg border border-white/30`:`${o()} hover:${f()} hover:shadow-md hover:border hover:border-white/20`}`,children:[t.jsxs("div",{className:"flex items-center",children:[t.jsx(V,{className:"mr-3 h-5 w-5"}),"Settings"]}),i?t.jsx(X,{className:"h-4 w-4"}):t.jsx(Q,{className:"h-4 w-4"})]}),i&&t.jsx("div",{className:"ml-4 space-y-2 mt-2",children:ae.map(c=>t.jsxs(z,{to:c.href,className:({isActive:g})=>`flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-all duration-300 ${g?`bg-white/30 backdrop-blur-sm ${o()} shadow-md border border-white/25`:`${o()} hover:bg-white/20 hover:backdrop-blur-sm hover:shadow-sm`}`,children:[t.jsx(c.icon,{className:"mr-3 h-4 w-4"}),c.name]},c.name))})]})]})]})}),r&&t.jsx("div",{className:`sidebar-container lg:hidden fixed inset-y-0 left-0 z-50 w-64 ${u()} transform transition-transform duration-300 ease-in-out shadow-2xl translate-x-0`,style:{paddingTop:"env(safe-area-inset-top, 0px)"},children:t.jsxs("div",{className:"flex flex-col h-full",children:[t.jsxs("div",{className:"flex items-center justify-between px-4 border-b border-white/15",style:{minHeight:"calc(4rem + env(safe-area-inset-top, 0px))",paddingTop:"calc(env(safe-area-inset-top, 0px) + 1rem)",paddingBottom:"1rem"},children:[t.jsx("div",{className:"flex items-center",children:t.jsx("h2",{className:`text-xl font-bold ${o()} tracking-wider drop-shadow-sm`,children:"MENU"})}),t.jsx("button",{onClick:e,className:`p-2 rounded-xl ${o()} hover:bg-white/15 hover:backdrop-blur-sm transition-all duration-300 shadow-sm`,children:t.jsx(Re,{className:"h-6 w-6"})})]}),t.jsxs("nav",{className:"flex-1 px-4 py-6 space-y-2",children:[ie.map(c=>t.jsxs(z,{to:c.href,onClick:e,className:({isActive:g})=>`flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-300 ${g?`${p()} ${o()} shadow-lg border border-white/30`:`${o()} hover:${f()} hover:shadow-md hover:border hover:border-white/20`}`,children:[t.jsx(c.icon,{className:"mr-3 h-5 w-5"}),c.name]},c.name)),t.jsxs("div",{className:"space-y-2",children:[t.jsxs("button",{onClick:()=>l(!i),className:`w-full flex items-center justify-between px-4 py-3 text-sm font-medium rounded-xl transition-all duration-300 ${s.pathname.includes("/settings")?`${p()} ${o()} shadow-lg border border-white/30`:`${o()} hover:${f()} hover:shadow-md hover:border hover:border-white/20`}`,children:[t.jsxs("div",{className:"flex items-center",children:[t.jsx(V,{className:"mr-3 h-5 w-5"}),"Settings"]}),i?t.jsx(X,{className:"h-4 w-4"}):t.jsx(Q,{className:"h-4 w-4"})]}),i&&t.jsx("div",{className:"ml-4 space-y-2 mt-2",children:ae.map(c=>t.jsxs(z,{to:c.href,onClick:e,className:({isActive:g})=>`flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-all duration-300 ${g?`bg-white/30 backdrop-blur-sm ${o()} shadow-md border border-white/25`:`${o()} hover:bg-white/20 hover:backdrop-blur-sm hover:shadow-sm`}`,children:[t.jsx(c.icon,{className:"mr-3 h-4 w-4"}),c.name]},c.name))})]})]})]})})]})}function dt(){const r=W(),e=he(),{currentTheme:n}=L(),a=()=>n==="dark"?"bg-gray-900/95 backdrop-blur-xl border-gray-700/50":{electric:"bg-blue-550/95 backdrop-blur-xl border-white/30",green:"bg-green-550/95 backdrop-blur-xl border-white/30",teal:"bg-teal-550/95 backdrop-blur-xl border-white/30",pink:"bg-pink-550/95 backdrop-blur-xl border-white/30"}[n]||"bg-blue-550/95 backdrop-blur-xl border-white/30",s=[{id:"dashboard",path:"/",icon:pe,label:"Dashboard"},{id:"purchases",path:"/purchases",icon:Ie,label:"Purchases"},{id:"usage",path:"/usage",icon:Oe,label:"Usage"},{id:"history",path:"/history",icon:be,label:"History"}],i=l=>l==="/"?r.pathname==="/"||r.pathname==="/dashboard":r.pathname===l;return t.jsx("div",{className:`md:hidden ${a()} border-t shadow-xl`,children:t.jsx("div",{className:"flex items-center justify-around px-1 py-0.5",children:s.map(l=>{const u=l.icon,o=i(l.path);return t.jsxs("button",{onClick:()=>e(l.path),className:`flex flex-col items-center justify-center p-1 rounded-lg transition-all duration-200 min-w-0 flex-1 ${o?"bg-white/20 text-white shadow-lg transform scale-105 border border-white/30":"text-white/80 hover:bg-white/10 hover:text-white"}`,children:[t.jsx(u,{className:`h-3.5 w-3.5 ${o?"text-white":"text-white/80"}`}),t.jsx("span",{className:`text-xs font-medium mt-0.5 truncate ${o?"text-white":"text-white/80"}`,children:l.label})]},l.id)})})})}const ce=r=>{r.target.type==="number"&&r.preventDefault()};function ut(){const[r,e]=h.useState(""),[n,a]=h.useState(""),[s,i]=h.useState(""),{initializeApp:l,state:u}=J(),{theme:o}=L(),f=p=>{p.preventDefault(),i("");const c=parseFloat(r),g=parseFloat(n);if(isNaN(c)||c<0){i("Please enter a valid number of units (0 or greater)");return}if(isNaN(g)||g<=0){i("Please enter a valid cost per unit (greater than 0)");return}l(c,g)};return t.jsx("div",{className:`min-h-screen ${o.background}`,children:t.jsx("div",{className:"h-screen overflow-y-auto",children:t.jsx("div",{className:"container mx-auto px-4 py-4",children:t.jsxs("div",{className:`max-w-lg mx-auto ${o.card} rounded-2xl shadow-2xl p-6 border ${o.border}`,children:[t.jsxs("div",{className:"text-center mb-6",children:[t.jsx("div",{className:"flex justify-center mb-4",children:t.jsxs("div",{className:"h-16 w-16 flex items-center justify-center",children:[t.jsx("img",{src:"/Logo Prepaid User.png",alt:"Prepaid User Electricity Logo",className:"h-16 w-16 object-contain",onError:p=>{p.target.style.display="none",p.target.nextElementSibling.style.display="flex"}}),t.jsx("div",{className:"h-16 w-16 rounded-full bg-blue-600 flex items-center justify-center shadow-2xl border-4 border-white",style:{display:"none"},children:t.jsx("span",{className:"text-white text-2xl font-bold",children:"⚡"})})]})}),t.jsx("h1",{className:`text-2xl font-bold ${o.text} mb-3`,children:"Welcome to Prepaid Meter App"}),t.jsx("p",{className:`${o.textSecondary} text-base mb-2`,children:"Let's get started by setting up your initial meter reading and cost settings"})]}),t.jsxs("form",{onSubmit:f,className:"space-y-4",children:[t.jsxs("div",{children:[t.jsx("label",{htmlFor:"initialUnits",className:`block text-sm font-medium ${o.text} mb-2`,children:"Initial Unit Value"}),t.jsx("input",{type:"number",id:"initialUnits",value:r,onChange:p=>e(p.target.value),onWheel:ce,step:"0.01",min:"0",placeholder:"Enter your current meter reading",className:`w-full px-3 py-3 border-2 ${o.border} rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${o.card} ${o.text} transition-all duration-200 hover:border-blue-300`,required:!0})]}),t.jsxs("div",{children:[t.jsxs("label",{htmlFor:"unitCost",className:`block text-sm font-medium ${o.text} mb-2`,children:["Cost per Unit (",u.currencySymbol||"R",")"]}),t.jsx("input",{type:"number",id:"unitCost",value:n,onChange:p=>a(p.target.value),onWheel:ce,step:"0.01",min:"0.01",placeholder:"Enter cost per unit (e.g., 2.50)",className:`w-full px-3 py-3 border-2 ${o.border} rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${o.card} ${o.text} transition-all duration-200 hover:border-blue-300`,required:!0})]}),s&&t.jsx("div",{className:"p-3 bg-red-50 border border-red-200 rounded-lg",children:t.jsx("p",{className:"text-sm text-red-600",children:s})}),(r||n)&&t.jsxs("div",{className:"mt-4 space-y-3",children:[t.jsxs("div",{className:`relative overflow-hidden rounded-xl ${o.card} border ${o.border} p-4 shadow-lg`,children:[t.jsx("div",{className:`absolute inset-0 ${o.secondary}`}),t.jsx("div",{className:"relative",children:t.jsxs("div",{className:"flex items-center justify-between",children:[t.jsxs("div",{className:"flex-1",children:[t.jsx("div",{className:`text-xs font-semibold ${o.textSecondary} tracking-wider uppercase mb-2`,children:"STARTING UNITS"}),t.jsx("p",{className:`text-xl font-bold ${o.text} mb-1`,children:parseFloat(r||0).toFixed(2)}),t.jsx("p",{className:`text-xs font-medium ${o.textSecondary}`,children:"Initial meter reading"})]}),t.jsx("div",{className:`p-2 rounded-lg bg-gradient-to-br ${o.gradient} shadow-lg ml-3`,children:t.jsx("span",{className:"text-white text-lg",children:"⚡"})})]})})]}),t.jsxs("div",{className:`relative overflow-hidden rounded-xl ${o.card} border ${o.border} p-4 shadow-lg`,children:[t.jsx("div",{className:`absolute inset-0 ${o.secondary}`}),t.jsx("div",{className:"relative",children:t.jsxs("div",{className:"flex items-center justify-between",children:[t.jsxs("div",{className:"flex-1",children:[t.jsx("div",{className:`text-xs font-semibold ${o.textSecondary} tracking-wider uppercase mb-2`,children:"UNIT COST"}),t.jsxs("p",{className:`text-xl font-bold ${o.text} mb-1`,children:[u.currencySymbol||"R",parseFloat(n||0).toFixed(2)]}),t.jsx("p",{className:`text-xs font-medium ${o.textSecondary}`,children:"Per unit rate"})]}),t.jsx("div",{className:`p-2 rounded-lg bg-gradient-to-br ${o.gradient} shadow-lg ml-3`,children:t.jsx("span",{className:"text-white text-lg",children:"💰"})})]})})]})]}),t.jsx("button",{type:"submit",className:`w-full bg-gradient-to-r ${o.gradient} text-white py-3 px-6 rounded-xl font-semibold hover:opacity-90 transition-all duration-200 focus:ring-2 focus:ring-opacity-50 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5`,children:"🚀 Initialize App"})]}),t.jsx("div",{className:"mt-4 text-center",children:t.jsx("p",{className:`text-xs ${o.textSecondary}`,children:"You can always change these values later in Settings"})})]})})})})}function O(){const r=ke(),{theme:e}=L();return console.error("Route Error:",r),Ee(r)?t.jsx("div",{className:`min-h-screen flex items-center justify-center ${e.background}`,children:t.jsxs("div",{className:`max-w-md mx-auto text-center p-6 ${e.card} rounded-lg shadow-lg`,children:[t.jsxs("h1",{className:`text-2xl font-bold mb-4 ${e.text}`,children:[r.status," ",r.statusText]}),t.jsx("p",{className:`${e.textSecondary} mb-4`,children:r.status===404?"The page you're looking for doesn't exist.":"Something went wrong with your request."}),t.jsx("button",{onClick:()=>window.location.href="/",className:`px-4 py-2 ${e.primary} text-white rounded-lg hover:opacity-90 transition-opacity`,children:"Go Home"})]})}):r instanceof Error?t.jsx("div",{className:`min-h-screen flex items-center justify-center ${e.background}`,children:t.jsxs("div",{className:`max-w-md mx-auto text-center p-6 ${e.card} rounded-lg shadow-lg`,children:[t.jsx("h1",{className:`text-2xl font-bold mb-4 ${e.text}`,children:"Oops! Something went wrong"}),t.jsx("p",{className:`${e.textSecondary} mb-4`,children:r.message}),!1,t.jsx("button",{onClick:()=>window.location.reload(),className:`px-4 py-2 ${e.primary} text-white rounded-lg hover:opacity-90 transition-opacity mr-2`,children:"Reload Page"}),t.jsx("button",{onClick:()=>window.location.href="/",className:`px-4 py-2 ${e.secondary} ${e.text} rounded-lg hover:opacity-90 transition-opacity`,children:"Go Home"})]})}):t.jsx("div",{className:`min-h-screen flex items-center justify-center ${e.background}`,children:t.jsxs("div",{className:`max-w-md mx-auto text-center p-6 ${e.card} rounded-lg shadow-lg`,children:[t.jsx("h1",{className:`text-2xl font-bold mb-4 ${e.text}`,children:"Unknown Error"}),t.jsx("p",{className:`${e.textSecondary} mb-4`,children:"An unexpected error occurred. Please try refreshing the page."}),t.jsx("button",{onClick:()=>window.location.reload(),className:`px-4 py-2 ${e.primary} text-white rounded-lg hover:opacity-90 transition-opacity`,children:"Reload Page"})]})})}function mt({size:r="md",text:e="Loading..."}){const{theme:n}=L(),a={sm:"h-4 w-4",md:"h-8 w-8",lg:"h-12 w-12",xl:"h-16 w-16"};return t.jsxs("div",{className:`flex flex-col items-center justify-center p-8 ${n.background}`,children:[t.jsx("div",{className:"relative",children:t.jsx("div",{className:`${a[r]} border-4 border-gray-200 rounded-full animate-spin`,children:t.jsx("div",{className:"absolute inset-0 border-4 border-transparent border-t-blue-500 rounded-full animate-spin"})})}),e&&t.jsx("p",{className:`mt-4 text-sm font-medium ${n.textSecondary} animate-pulse`,children:e})]})}const le=h.lazy(()=>U(()=>import("../js/Dashboard-DN1UwnOq.js"),__vite__mapDeps([4,1,5,3,6,2]))),ft=h.lazy(()=>U(()=>import("../js/Purchases-COGkPscP.js"),__vite__mapDeps([7,1,3,6,2]))),gt=h.lazy(()=>U(()=>import("../js/Usage-BlNK_qXz.js"),__vite__mapDeps([8,5,1,3,9,6,2]))),ht=h.lazy(()=>U(()=>import("../js/History-LbXCAQK-.js"),__vite__mapDeps([10,1,3,9,2]))),pt=h.lazy(()=>U(()=>import("../js/Settings-dA8Y6xUM.js"),__vite__mapDeps([11,1,3,2])));function bt(){const[r,e]=h.useState(!1),{state:n}=J(),{theme:a,currentTheme:s}=L(),i=W(),l=()=>({electric:"safe-area-electric",green:"safe-area-green",teal:"safe-area-teal",pink:"safe-area-pink",dark:"safe-area-dark"})[s]||"safe-area-electric";return h.useEffect(()=>{const u=o=>{o.target.type==="number"&&document.activeElement===o.target&&o.preventDefault()};return document.addEventListener("wheel",u,{passive:!1}),()=>{document.removeEventListener("wheel",u)}},[]),h.useEffect(()=>{const u=o=>{r&&!o.target.closest(".sidebar-container")&&!o.target.closest(".hamburger-menu")&&e(!1)};return document.addEventListener("mousedown",u),()=>{document.removeEventListener("mousedown",u)}},[r]),h.useEffect(()=>{e(!1)},[i.pathname]),h.useEffect(()=>{e(!1);const u=()=>{window.innerWidth<1024&&e(!1)};return window.addEventListener("resize",u),()=>window.removeEventListener("resize",u)},[]),n.isInitialized?t.jsxs("div",{className:`flex flex-col h-screen ${a.background}`,style:{height:"100vh"},children:[t.jsx("div",{className:`${l()} safe-top mobile-safe-top flex-shrink-0`}),t.jsxs("div",{className:"flex flex-1 overflow-hidden",children:[r&&t.jsx("div",{className:"fixed inset-0 bg-black/50 z-40 lg:hidden",onClick:()=>e(!1),style:{zIndex:40}}),t.jsx(lt,{isOpen:r,onClose:()=>e(!1)}),t.jsxs("div",{className:"flex flex-col flex-1 overflow-hidden",children:[t.jsx("div",{className:"flex-shrink-0",children:t.jsx(ct,{onMenuClick:()=>e(u=>!u)})}),t.jsx("main",{className:"flex-1 overflow-y-auto overflow-x-hidden px-4 lg:px-8 pt-2 safe-bottom-nav mobile-safe-bottom-nav mobile-content",style:{WebkitOverflowScrolling:"touch",touchAction:"pan-y",overscrollBehavior:"contain"},children:t.jsx("div",{className:"w-full pb-20",children:t.jsx(h.Suspense,{fallback:t.jsx(mt,{size:"lg",text:"Loading page..."}),children:t.jsxs(Pe,{children:[t.jsx(I,{path:"/",element:t.jsx(le,{}),errorElement:t.jsx(O,{})}),t.jsx(I,{path:"/dashboard",element:t.jsx(le,{}),errorElement:t.jsx(O,{})}),t.jsx(I,{path:"/purchases",element:t.jsx(ft,{}),errorElement:t.jsx(O,{})}),t.jsx(I,{path:"/usage",element:t.jsx(gt,{}),errorElement:t.jsx(O,{})}),t.jsx(I,{path:"/history",element:t.jsx(ht,{}),errorElement:t.jsx(O,{})}),t.jsx(I,{path:"/settings",element:t.jsx(pt,{}),errorElement:t.jsx(O,{})}),t.jsx(I,{path:"*",element:t.jsx(O,{})})]})})})}),t.jsx("div",{className:"flex-shrink-0 lg:hidden",children:t.jsx(dt,{})})]})]}),t.jsx("div",{className:`${l()} safe-bottom mobile-safe-bottom flex-shrink-0`})]}):t.jsx(ut,{})}const de=F("App",{web:()=>U(()=>import("../js/web-BDcoYDXJ.js"),__vite__mapDeps([12,1,2,3])).then(r=>new r.AppWeb)}),xt=()=>{const r=he(),e=W();h.useEffect(()=>{if(!P.isNativePlatform()||P.getPlatform()!=="android")return;const n=()=>{const s=e.pathname,i={"/purchases":"/","/usage":"/","/history":"/","/settings":"/"};if(i[s]){r(i[s]);return}if(s==="/"){de.exitApp();return}r("/")},a=de.addListener("backButton",n);return()=>{a.remove()}},[r,e.pathname])};var ue;(function(r){r.Dark="DARK",r.Light="LIGHT",r.Default="DEFAULT"})(ue||(ue={}));var me;(function(r){r.None="NONE",r.Slide="SLIDE",r.Fade="FADE"})(me||(me={}));const _=F("StatusBar");var fe;(function(r){r.WHITE="#FFFFFF",r.BLACK="#000000",r.TRANSPARENT="transparent"})(fe||(fe={}));const ge=F("NavigationBar",{web:()=>U(()=>import("../js/web-CeFd6JiW.js"),__vite__mapDeps([13,1,2,3])).then(r=>new r.NavigationBarWeb)});function yt(){const{currentTheme:r}=L();h.useEffect(()=>{if(!P.isNativePlatform())return;(async()=>{try{if(await _.setOverlaysWebView({overlay:!1}),r==="dark")await _.setStyle({style:"LIGHT"}),await _.setBackgroundColor({color:"#111827"});else{await _.setStyle({style:"LIGHT"});const a={electric:"#3B82F6",green:"#10B981",teal:"#14B8A6",pink:"#EC4899"}[r]||"#3B82F6";await _.setBackgroundColor({color:a})}if(await _.show(),r==="dark")await ge.setColor({color:"#111827",darkButtons:!1});else{const a={electric:"#3B82F6",green:"#10B981",teal:"#14B8A6",pink:"#EC4899"}[r]||"#3B82F6";await ge.setColor({color:a,darkButtons:!1})}}catch(n){console.log("StatusBar or NavigationBar plugin not available:",n)}})()},[r])}function wt(){return xt(),yt(),t.jsx(bt,{})}function vt(){return t.jsx(Te,{children:t.jsx(it,{children:t.jsx(st,{children:t.jsx(wt,{})})})})}we(document.getElementById("root")).render(t.jsx(h.StrictMode,{children:t.jsx(vt,{})}));export{ve as W,L as a,t as j,ce as p,B as t,J as u};
