import { useEffect } from 'react';
import { StatusBar } from '@capacitor/status-bar';
import { NavigationBar } from '@capgo/capacitor-navigation-bar';
import { Capacitor } from '@capacitor/core';
import { useTheme } from '../context/ThemeContext';

export function useStatusBar() {
  const { currentTheme } = useTheme();

  useEffect(() => {
    // Only run on mobile platforms
    if (!Capacitor.isNativePlatform()) {
      return;
    }

    const updateStatusBar = async () => {
      try {
        // Always ensure status bar is visible and doesn't overlay content
        await StatusBar.setOverlaysWebView({ overlay: false });

        // Configure status bar based on current theme to match header background exactly
        if (currentTheme === 'dark') {
          // Dark theme: light content on dark background matching header
          await StatusBar.setStyle({ style: 'LIGHT' });
          await StatusBar.setBackgroundColor({ color: '#111827' }); // gray-900 to match header bg-gray-900/95
        } else {
          // Light themes: light content on theme-colored background matching header exactly
          await StatusBar.setStyle({ style: 'LIGHT' });

          // Set background color to match header background exactly (bg-[color]-500/90)
          const themeColors = {
            'electric': '#3B82F6', // blue-500 to match header bg-blue-500/90
            'green': '#10B981',    // green-500 to match header bg-green-500/90
            'teal': '#14B8A6',     // teal-500 to match header bg-teal-500/90
            'pink': '#EC4899'      // pink-500 to match header bg-pink-500/90
          };

          const backgroundColor = themeColors[currentTheme] || '#3B82F6';
          await StatusBar.setBackgroundColor({ color: backgroundColor });
        }

        // Show the status bar if it was hidden
        await StatusBar.show();

        // Configure navigation bar to match status bar colors
        if (currentTheme === 'dark') {
          // Dark theme: dark navigation bar
          await NavigationBar.setColor({ color: '#111827', darkButtons: false });
        } else {
          // Light themes: theme-colored navigation bar with light buttons
          const themeColors = {
            'electric': '#3B82F6', // blue-500 to match header
            'green': '#10B981',    // green-500 to match header
            'teal': '#14B8A6',     // teal-500 to match header
            'pink': '#EC4899'      // pink-500 to match header
          };

          const backgroundColor = themeColors[currentTheme] || '#3B82F6';
          await NavigationBar.setColor({ color: backgroundColor, darkButtons: false });
        }

      } catch (error) {
        console.log('StatusBar or NavigationBar plugin not available:', error);
      }
    };

    updateStatusBar();
  }, [currentTheme]);
}
