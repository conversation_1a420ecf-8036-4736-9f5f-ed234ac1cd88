import { useState, useEffect, useRef } from 'react'
import { useNavigate } from 'react-router-dom'
import { useApp } from '../../context/AppContext'
import { useTheme } from '../../context/ThemeContext'
import HistoryTable from './HistoryTable'
import { HiClipboardList, HiCurrencyDollar, HiTrendingUp, HiFilter } from 'react-icons/hi'

function History() {
  const navigate = useNavigate()
  const [activeTab, setActiveTab] = useState('all')
  const [dateFilter, setDateFilter] = useState('')
  const [showDateRangePicker, setShowDateRangePicker] = useState(false)
  const [fromDate, setFromDate] = useState('')
  const [toDate, setToDate] = useState('')
  const dateRangeRef = useRef(null)

  const {
    state
  } = useApp()
  const { theme, currentTheme } = useTheme()

  // Combine and sort all history
  const allHistory = [
    ...state.purchases.map(item => ({ ...item, type: 'purchase' })),
    ...state.usageHistory.map(item => ({ ...item, type: 'usage' }))
  ].sort((a, b) => new Date(b.date) - new Date(a.date))

  // Filter history based on active tab and date
  const filteredHistory = allHistory.filter(item => {
    const matchesTab = activeTab === 'all' || item.type === activeTab

    // Handle single date filter
    if (dateFilter && !fromDate && !toDate) {
      return matchesTab && item.date.includes(dateFilter)
    }

    // Handle date range filter
    if (fromDate || toDate) {
      const itemDate = new Date(item.date)
      const from = fromDate ? new Date(fromDate) : null
      const to = toDate ? new Date(toDate) : null

      let matchesDateRange = true
      if (from) {
        matchesDateRange = matchesDateRange && itemDate >= from
      }
      if (to) {
        matchesDateRange = matchesDateRange && itemDate <= to
      }

      return matchesTab && matchesDateRange
    }

    return matchesTab
  })

  // Calculate statistics

  const tabs = [
    { id: 'all', name: 'All Activity', icon: HiClipboardList },
    { id: 'purchase', name: 'Purchases', icon: HiCurrencyDollar },
    { id: 'usage', name: 'Usage', icon: HiTrendingUp },
  ]

  // Handle date range application
  const applyDateRange = () => {
    setDateFilter('') // Clear single date filter when using range
    setShowDateRangePicker(false)
  }

  // Clear all date filters
  const clearAllDateFilters = () => {
    setDateFilter('')
    setFromDate('')
    setToDate('')
    setShowDateRangePicker(false)
  }

  // Check if any date filter is active
  const hasActiveFilters = dateFilter || fromDate || toDate



  // Close date range picker when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dateRangeRef.current && !dateRangeRef.current.contains(event.target)) {
        setShowDateRangePicker(false)
      }
    }

    if (showDateRangePicker) {
      document.addEventListener('mousedown', handleClickOutside)
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [showDateRangePicker])



  return (
    <div className="space-y-6">




      {/* Filters and tabs */}
      <div className={`${theme.card} rounded-2xl shadow-lg border ${theme.border}`}>
        <div className={`p-4 md:p-8 ${theme.secondary} rounded-t-2xl`}>
          <div className="space-y-4">
            {/* Tabs */}
            <div className="flex flex-wrap gap-2">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-all duration-300 border backdrop-blur-md shadow-lg ${
                    activeTab === tab.id
                      ? `${theme.primary} text-white border-white/30 bg-opacity-90 shadow-xl`
                      : currentTheme === 'dark'
                        ? `${theme.text} bg-white/10 border-white/20 hover:bg-white/20 hover:border-white/30 hover:shadow-xl`
                        : `${theme.text} bg-white/30 border-white/40 hover:bg-white/50 hover:border-white/60 hover:shadow-xl`
                  }`}
                >
                  <tab.icon className="mr-2 h-4 w-4" />
                  {tab.name}
                </button>
              ))}
            </div>



            {/* Date filter controls */}
            <div ref={dateRangeRef} className="space-y-3">
              <div className="flex items-center gap-2 mb-2">
                <HiFilter className={`h-5 w-5 ${theme.textSecondary}`} />
                <span className={`text-sm font-medium ${theme.text}`}>Filter by Date</span>
              </div>

              <div className="flex flex-col sm:flex-row gap-3">
                {/* Single date filter */}
                <input
                  type="date"
                  value={dateFilter || new Date().toISOString().split('T')[0]}
                  onChange={(e) => {
                    setDateFilter(e.target.value)
                    if (e.target.value) {
                      setFromDate('')
                      setToDate('')
                    }
                  }}
                  className={`px-3 py-2 border ${theme.border} rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${theme.card} ${theme.text} text-sm w-full sm:w-40`}
                  placeholder="Filter by date"
                />

                {/* Date range picker button */}
                <button
                  onClick={() => setShowDateRangePicker(!showDateRangePicker)}
                  className={`px-3 py-2 border ${theme.border} rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${theme.card} ${theme.text} hover:${theme.secondary} transition-colors text-sm ${(fromDate || toDate) ? 'ring-2 ring-blue-500' : ''}`}
                >
                  📅 Range
                </button>

                {/* Clear filters button */}
                {hasActiveFilters && (
                  <button
                    onClick={clearAllDateFilters}
                    className={`px-3 py-2 text-sm ${theme.textSecondary} hover:${theme.text} transition-colors border ${theme.border} rounded-lg`}
                  >
                    Clear All
                  </button>
                )}
              </div>

              {/* Date Range Picker Dropdown */}
              {showDateRangePicker && (
                <div className={`mt-3 p-4 ${theme.card} border ${theme.border} rounded-xl shadow-xl w-full max-w-md`}>
                  <div className="space-y-4">
                    <h3 className={`font-semibold ${theme.text} text-sm`}>Select Date Range</h3>

                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      <div>
                        <label className={`block text-xs font-medium ${theme.textSecondary} mb-1`}>
                          From Date
                        </label>
                        <input
                          type="date"
                          value={fromDate}
                          onChange={(e) => {
                            setFromDate(e.target.value)
                            setDateFilter('') // Clear single date when using range
                          }}
                          className={`w-full px-3 py-2 border ${theme.border} rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${theme.card} ${theme.text} text-sm`}
                        />
                      </div>

                      <div>
                        <label className={`block text-xs font-medium ${theme.textSecondary} mb-1`}>
                          To Date
                        </label>
                        <input
                          type="date"
                          value={toDate}
                          onChange={(e) => {
                            setToDate(e.target.value)
                            setDateFilter('') // Clear single date when using range
                          }}
                          className={`w-full px-3 py-2 border ${theme.border} rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${theme.card} ${theme.text} text-sm`}
                        />
                      </div>
                    </div>

                    <div className="flex justify-between items-center pt-2">
                      <button
                        onClick={() => {
                          setFromDate('')
                          setToDate('')
                        }}
                        className={`text-xs ${theme.textSecondary} hover:${theme.text} transition-colors`}
                      >
                        Clear Range
                      </button>

                      <button
                        onClick={applyDateRange}
                        className={`px-4 py-2 bg-gradient-to-r ${theme.gradient} text-white rounded-lg hover:opacity-90 transition-colors text-sm font-medium`}
                      >
                        Apply
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* History table */}
        <div className={`border-t ${theme.border}`}>
          <HistoryTable history={filteredHistory} />
        </div>
      </div>

      {/* Empty state */}
      {filteredHistory.length === 0 && (
        <div className={`${theme.card} rounded-2xl shadow-lg p-16 text-center border ${theme.border}`}>
          <div className="relative">
            <div className={`absolute inset-0 ${theme.secondary} rounded-full opacity-20 scale-110`}></div>
            <div className={`relative p-6 rounded-2xl ${theme.secondary} w-fit mx-auto`}>
              <HiClipboardList className={`h-16 w-16 ${theme.textSecondary}`} />
            </div>
          </div>
          <h3 className={`mt-6 text-2xl font-bold ${theme.text}`}>
            No history found
          </h3>
          <p className={`mt-3 ${theme.textSecondary} opacity-80 text-lg leading-relaxed max-w-md mx-auto`}>
            {hasActiveFilters
              ? 'No records found for the selected date range. Try adjusting your filters or clear them to see all records.'
              : 'Start by making purchases or recording usage to see your history here.'
            }
          </p>
          {!hasActiveFilters && (
            <div className="mt-8 flex flex-col sm:flex-row justify-center gap-4">
              <button
                onClick={() => navigate('/purchases')}
                className={`bg-gradient-to-r ${theme.gradient} text-white px-6 py-3 rounded-xl font-semibold hover:opacity-90 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]`}
              >
                <div className="flex items-center gap-2">
                  <span>💰</span>
                  Add Purchase
                </div>
              </button>
              <button
                onClick={() => navigate('/usage')}
                className={`bg-gradient-to-r ${theme.gradient} text-white px-6 py-3 rounded-xl font-semibold hover:opacity-90 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]`}
              >
                <div className="flex items-center gap-2">
                  <span>⚡</span>
                  Record Usage
                </div>
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  )
}

export default History
