// Test utility to verify Android back button functionality
import { App } from '@capacitor/app';
import { Capacitor } from '@capacitor/core';

export const testBackButtonSetup = () => {
  if (!Capacitor.isNativePlatform() || Capacitor.getPlatform() !== 'android') {
    console.log('Back button test: Not on Android platform');
    return false;
  }

  console.log('Back button test: Android platform detected');
  console.log('Back button test: App plugin available:', !!App);
  console.log('Back button test: addListener method available:', !!App.addListener);
  
  return true;
};

export const logBackButtonEvent = () => {
  console.log('Back button test: Back button pressed!');
};
