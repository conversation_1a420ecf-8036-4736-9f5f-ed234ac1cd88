import { useState, useEffect, lazy, Suspense } from 'react'
import { Routes, Route, useLocation } from 'react-router-dom'
import Header from './Header'
import Sidebar from './Sidebar'
import MobileFooterNav from './MobileFooterNav'
import InitialSetup from '../Common/InitialSetup'
import { RouteErrorBoundary } from '../Common/ErrorBoundary'
import LoadingSpinner from '../Common/LoadingSpinner'
import { useApp } from '../../context/AppContext'
import { useTheme } from '../../context/ThemeContext'

// Lazy load components for better code splitting
const Dashboard = lazy(() => import('../Dashboard/Dashboard'))
const Purchases = lazy(() => import('../Purchases/Purchases'))
const Usage = lazy(() => import('../Usage/Usage'))
const History = lazy(() => import('../History/History'))
const Settings = lazy(() => import('../Settings/Settings'))

function Layout() {
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const { state } = useApp()
  const { theme, currentTheme } = useTheme()
  const location = useLocation()



  // Get theme-aware safe area class
  const getSafeAreaClass = () => {
    const themeMap = {
      'electric': 'safe-area-electric',
      'green': 'safe-area-green',
      'teal': 'safe-area-teal',
      'pink': 'safe-area-pink',
      'dark': 'safe-area-dark'
    }
    return themeMap[currentTheme] || 'safe-area-electric'
  }

  // Prevent scroll wheel from changing number input values
  useEffect(() => {
    const preventNumberInputScroll = (e) => {
      // Check if the target is a number input and it's focused
      if (e.target.type === 'number' && document.activeElement === e.target) {
        e.preventDefault()
      }
    }

    // Add event listener to prevent scroll on number inputs
    document.addEventListener('wheel', preventNumberInputScroll, { passive: false })

    // Cleanup function
    return () => {
      document.removeEventListener('wheel', preventNumberInputScroll)
    }
  }, [])

  // Close sidebar when clicking outside or on mobile
  useEffect(() => {
    const handleClickOutside = (event) => {
      // Close sidebar if clicking outside of it
      if (sidebarOpen && !event.target.closest('.sidebar-container') && !event.target.closest('.hamburger-menu')) {
        setSidebarOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [sidebarOpen])

  // Close sidebar on route change and on mount
  useEffect(() => {
    setSidebarOpen(false)
  }, [location.pathname])

  // Force close sidebar on initial load and handle window resize
  useEffect(() => {
    setSidebarOpen(false)

    const handleResize = () => {
      if (window.innerWidth < 1024) {
        setSidebarOpen(false)
      }
    }

    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  // Show initial setup if app is not initialized
  if (!state.isInitialized) {
    return <InitialSetup />
  }

  return (
    <div className={`flex flex-col h-screen ${theme.background}`}
         style={{ height: '100vh' }}>
      {/* Top Safe Area - Theme-aware and narrower */}
      <div className={`${getSafeAreaClass()} safe-top mobile-safe-top flex-shrink-0`} />

      {/* Desktop Layout: Sidebar + Content */}
      <div className="flex flex-1 overflow-hidden">
        {/* Sidebar - Always visible on desktop, toggleable on mobile */}
        {sidebarOpen && (
          <div
            className="fixed inset-0 bg-black/50 z-40 lg:hidden"
            onClick={() => setSidebarOpen(false)}
            style={{ zIndex: 40 }}
          />
        )}
        <Sidebar isOpen={sidebarOpen} onClose={() => setSidebarOpen(false)} />

        {/* Main Content Area */}
        <div className="flex flex-col flex-1 overflow-hidden">
          {/* Header - Fixed at top */}
          <div className="flex-shrink-0">
            <Header onMenuClick={() => setSidebarOpen(prev => !prev)} />
          </div>

          {/* Main content - Scrollable middle section with proper mobile handling */}
          <main className="flex-1 overflow-y-auto overflow-x-hidden px-4 lg:px-8 pt-2 safe-bottom-nav mobile-safe-bottom-nav mobile-content"
                style={{
                  WebkitOverflowScrolling: 'touch',
                  touchAction: 'pan-y',
                  overscrollBehavior: 'contain'
                }}>
            <div className="w-full pb-20">
              <Suspense fallback={<LoadingSpinner size="lg" text="Loading page..." />}>
                <Routes>
                  <Route path="/" element={<Dashboard />} errorElement={<RouteErrorBoundary />} />
                  <Route path="/dashboard" element={<Dashboard />} errorElement={<RouteErrorBoundary />} />
                  <Route path="/purchases" element={<Purchases />} errorElement={<RouteErrorBoundary />} />
                  <Route path="/usage" element={<Usage />} errorElement={<RouteErrorBoundary />} />
                  <Route path="/history" element={<History />} errorElement={<RouteErrorBoundary />} />
                  <Route path="/settings" element={<Settings />} errorElement={<RouteErrorBoundary />} />
                  <Route path="*" element={<RouteErrorBoundary />} />
                </Routes>
              </Suspense>
            </div>
          </main>

          {/* Mobile Footer Navigation - Fixed at bottom */}
          <div className="flex-shrink-0 lg:hidden">
            <MobileFooterNav />
          </div>
        </div>
      </div>

      {/* Bottom Safe Area - Theme-aware and narrower */}
      <div className={`${getSafeAreaClass()} safe-bottom mobile-safe-bottom flex-shrink-0`} />
    </div>
  )
}

export default Layout
